import time
import requests
import json
from typing import <PERSON><PERSON>, Dict, Any

def measure_ollama_tps(prompt: str, model_name: str = "llama3", 
                      max_tokens: int = 100, host: str = "localhost", port: int = 11434) -> Tuple[float, int]:
    """
    Measure tokens per second for Ollama served LLM
    
    Args:
        prompt (str): Input text to process
        model_name (str): Name of the Ollama model to use
        max_tokens (int): Maximum number of tokens to generate
        host (str): Ollama server host
        port (int): Ollama server port
    
    Returns:
        Tuple[float, int]: (tokens_per_second, total_generated_tokens)
    """
    
    # Ollama API endpoint
    url = f"http://{host}:{port}/api/generate"
    
    # Prepare the request payload
    payload = {
        "model": model_name,
        "prompt": prompt,
        "stream": False,
        "options": {
            "num_predict": max_tokens
        }
    }
    
    # Start timing
    start_time = time.time()
    
    try:
        # Send request to Ollama
        response = requests.post(url, json=payload, timeout=30)
        response.raise_for_status()
        
        # Parse response
        result = response.json()
        generated_text = result.get("response", "")
        
        # End timing
        end_time = time.time()
        
        # Calculate tokens per second
        # Approximate token count (you can replace this with actual token counting)
        generated_tokens = len(generated_text.split())
        total_time = end_time - start_time
        
        if total_time > 0:
            tps = generated_tokens / total_time
        else:
            tps = 0
            
        return tps, generated_tokens
        
    except requests.exceptions.RequestException as e:
        print(f"Error communicating with Ollama: {e}")
        return 0, 0

def measure_ollama_tps_detailed(prompt: str, model_name: str = "llama3", 
                               max_tokens: int = 100, host: str = "localhost", port: int = 11434) -> Dict[str, Any]:
    """
    Measure tokens per second with detailed timing information
    
    Returns:
        Dict with performance metrics and timing details
    """
    
    url = f"http://{host}:{port}/api/generate"
    
    payload = {
        "model": model_name,
        "prompt": prompt,
        "stream": False,
        "options": {
            "num_predict": max_tokens
        }
    }
    
    # Start timing
    start_time = time.time()
    
    try:
        # Send request to Ollama
        response = requests.post(url, json=payload, timeout=30)
        response.raise_for_status()
        
        # Parse response
        result = response.json()
        generated_text = result.get("response", "")
        total_time = time.time() - start_time
        
        # Calculate tokens per second
        generated_tokens = len(generated_text.split())
        
        if total_time > 0:
            tps = generated_tokens / total_time
        else:
            tps = 0
            
        return {
            "tokens_per_second": tps,
            "generated_tokens": generated_tokens,
            "total_time_seconds": total_time,
            "model_name": model_name,
            "prompt_length": len(prompt),
            "response": generated_text[:100] + "..." if len(generated_text) > 100 else generated_text
        }
        
    except requests.exceptions.RequestException as e:
        print(f"Error communicating with Ollama: {e}")
        return {
            "tokens_per_second": 0,
            "generated_tokens": 0,
            "total_time_seconds": 0,
            "error": str(e)
        }

def main():
    # Configuration
    model_name = "llama3"  # Change to your Ollama model name
    prompt = "The quick brown fox jumps over the lazy dog. This is a test sentence to measure token generation speed."
    max_tokens = 100
    
    print("Measuring tokens per second for Ollama model...")
    print(f"Model: {model_name}")
    print(f"Prompt: {prompt}")
    print("-" * 50)
    
    # Method 1: Simple measurement
    tps, generated_tokens = measure_ollama_tps(
        prompt=prompt,
        model_name=model_name,
        max_tokens=max_tokens
    )
    
    print(f"\nSimple Measurement:")
    print(f"Generated tokens: {generated_tokens}")
    print(f"Tokens per second: {tps:.2f}")
    
    # Method 2: Detailed measurement
    print("\nDetailed Measurement:")
    detailed_results = measure_ollama_tps_detailed(
        prompt=prompt,
        model_name=model_name,
        max_tokens=max_tokens
    )
    
    for key, value in detailed_results.items():
        print(f"{key}: {value}")

def benchmark_multiple_models():
    """Benchmark multiple Ollama models"""
    models = ["llama3", "mistral", "phi3", "gemma"]
    prompt = "Explain the concept of artificial intelligence in simple terms."
    max_tokens = 50
    
    print("Benchmarking multiple Ollama models:")
    print("-" * 60)
    
    for model in models:
        try:
            results = measure_ollama_tps_detailed(
                prompt=prompt,
                model_name=model,
                max_tokens=max_tokens
            )
            
            if results["tokens_per_second"] > 0:
                print(f"{model:10} | {results['tokens_per_second']:6.2f} TPS | {results['generated_tokens']:3d} tokens")
            else:
                print(f"{model:10} | Error measuring performance")
                
        except Exception as e:
            print(f"{model:10} | Error: {e}")

if __name__ == "__main__":
    # Run single model test
    main()
    
    print("\n" + "="*60)
    
    # Run benchmark of multiple models (uncomment to run)
    # benchmark_multiple_models()
